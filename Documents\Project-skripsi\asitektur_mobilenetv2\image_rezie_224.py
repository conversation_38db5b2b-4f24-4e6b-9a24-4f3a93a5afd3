import os
import cv2
import numpy as np
from multiprocessing import Pool
from tqdm import tqdm
import zipfile
import glob


DATASET_PATH = r'Dataset'
IMG_SIZE = 224


def zip_and_remove(path):
    """Zip folder and remove original files"""
    ziph = zipfile.ZipFile(f'{path}.zip', 'w', zipfile.ZIP_DEFLATED)

    for root, dirs, files in os.walk(path):
        for file in tqdm(files, desc="Zipping files"):
            file_path = os.path.join(root, file)
            ziph.write(file_path, os.path.relpath(file_path, path))
            os.remove(file_path)

    ziph.close()
    # Remove empty directories
    for root, dirs, files in os.walk(path, topdown=False):
        for dir in dirs:
            dir_path = os.path.join(root, dir)
            try:
                os.rmdir(dir_path)
            except OSError:
                pass
    try:
        os.rmdir(path)
    except OSError:
        pass


def img_proc(img_info):
    """Process single image - resize to 224x224"""
    input_path, output_path = img_info
    try:
        img = cv2.imread(input_path)
        if img is not None:
            img_resized = cv2.resize(img, (IMG_SIZE, IMG_SIZE))
            cv2.imwrite(output_path, img_resized)
            return True
        else:
            print(f"Warning: Could not read image {input_path}")
            return False
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        return False


def imap_unordered_bar(func, args, n_processes: int = 4):
    """Process images with multiprocessing and progress bar"""
    p = Pool(n_processes, maxtasksperchild=100)
    res_list = []
    with tqdm(total=len(args), desc="Processing images") as pbar:
        for i, res in enumerate(p.imap_unordered(func, args)):
            pbar.update()
            res_list.append(res)
    pbar.close()
    p.close()
    p.join()
    return res_list


def main():
    # Create output directory
    output_dir = 'resized_images_224'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Get all image files from dataset
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    all_images = []

    for ext in image_extensions:
        pattern = os.path.join(DATASET_PATH, '**', ext)
        all_images.extend(glob.glob(pattern, recursive=True))

    print(f"Found {len(all_images)} images to process")

    # Prepare image processing arguments
    img_args = []
    for img_path in all_images:
        # Get relative path from dataset root
        rel_path = os.path.relpath(img_path, DATASET_PATH)
        # Create output path maintaining folder structure
        output_path = os.path.join(output_dir, rel_path)

        # Create output directory if it doesn't exist
        output_folder = os.path.dirname(output_path)
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        img_args.append((img_path, output_path))

    # Process images
    if img_args:
        results = imap_unordered_bar(img_proc, img_args, n_processes=4)
        successful = sum(results)
        print(f"Successfully processed {successful}/{len(img_args)} images")

        # Optionally zip the results
        print("Creating zip file...")
        zip_and_remove(output_dir)
        print(f"Completed! Check {output_dir}.zip")
    else:
        print("No images found to process!")


if __name__ == '__main__':
    main()