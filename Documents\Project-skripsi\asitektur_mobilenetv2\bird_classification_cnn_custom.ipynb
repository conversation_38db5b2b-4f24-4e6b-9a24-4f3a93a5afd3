# Install dependencies jika diperlukan
# !pip install tensorflow matplotlib seaborn pillow scikit-learn

import os
import sys
import shutil
import pathlib
import natsort

import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns

from PIL import Image
from tqdm import tqdm
from tensorflow.keras.preprocessing import image
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    confusion_matrix,
    classification_report
)

print("✅ Libraries imported successfully!")
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {tf.config.list_physical_devices('GPU')}")

# ===== KONFIGURASI DATASET BURUNG =====
print("🔍 Mendeteksi dataset burung yang tersedia...")

# Prioritas direktori data (dari yang paling diutamakan)
data_options = [
    ('./ResizedDataset', 'Data yang sudah diproses ke 224x224 - RECOMMENDED'),
    ('./Dataset', 'Data asli dengan berbagai ukuran'),
    ('./SplitDataset/train', 'Data yang sudah di-split untuk training'),
    ('./data', 'Direktori data alternatif')
]

selected_data_dir = None
dataset_info = {}

# Cek setiap opsi direktori
for data_dir, description in data_options:
    if os.path.exists(data_dir):
        print(f"\n📁 Ditemukan: {data_dir}")
        print(f"   📝 {description}")
        
        # Cek subdirectory dan hitung gambar
        subdirs = []
        total_images = 0
        
        for item in os.listdir(data_dir):
            item_path = os.path.join(data_dir, item)
            if os.path.isdir(item_path):
                # Hitung gambar di kelas ini
                image_files = [f for f in os.listdir(item_path) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])
                if image_files:  # Hanya tambahkan jika ada gambar
                    subdirs.append({
                        'name': item,
                        'count': len(image_files)
                    })
                    total_images += len(image_files)
        
        if subdirs:  # Jika ada kelas dengan gambar
            print(f"   📊 {len(subdirs)} kelas, {total_images} gambar total")
            print(f"   📋 Kelas yang ditemukan:")
            for subdir in subdirs:
                print(f"      - {subdir['name']}: {subdir['count']} gambar")
            
            # Gunakan direktori pertama yang valid
            if not selected_data_dir:
                selected_data_dir = data_dir
                dataset_info = {
                    'classes': subdirs,
                    'total_images': total_images,
                    'num_classes': len(subdirs)
                }
                print(f"\n✅ DIPILIH: {data_dir}")

# Hasil deteksi
if selected_data_dir:
    main_data_dir = selected_data_dir
    num_classes = dataset_info['num_classes']
    
    print(f"\n" + "="*60)
    print(f"🎯 KONFIGURASI DATA FINAL")
    print(f"="*60)
    print(f"📁 Data directory: {main_data_dir}")
    print(f"📊 Jumlah kelas: {num_classes}")
    print(f"📊 Total gambar: {dataset_info['total_images']}")
    print(f"🔧 Batch size: 16 (disesuaikan untuk CNN custom)")
    print(f"📊 Validation split: 20%")
    
    # Buat mapping kelas burung
    bird_classes = [cls['name'] for cls in dataset_info['classes']]
    print(f"\n🐦 Kelas burung: {bird_classes}")
    
else:
    print(f"\n❌ TIDAK DITEMUKAN DATASET YANG VALID!")
    # Fallback
    main_data_dir = './Dataset'
    num_classes = 4
    bird_classes = ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus']

# ===== DATA AUGMENTATION SETUP =====
print("🔄 Setting up data augmentation...")

# Buat direktori preview jika belum ada
preview_dir = './preview_augmentation'
os.makedirs(preview_dir, exist_ok=True)

# Ambil sample gambar untuk preview augmentasi
sample_found = False
for class_name in bird_classes:
    class_path = os.path.join(main_data_dir, class_name)
    if os.path.exists(class_path):
        image_files = [f for f in os.listdir(class_path) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])
        if image_files:
            sample_image_path = os.path.join(class_path, image_files[0])
            sample_found = True
            break

if sample_found:
    # Load dan konversi gambar ke array untuk preview
    img_augmentation = image.load_img(sample_image_path, target_size=(224, 224))
    x_aug = image.img_to_array(img_augmentation)
    x_aug = x_aug.reshape((1,) + x_aug.shape)
    
    # Setup augmentasi yang disesuaikan untuk burung
    preview_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=15,        # Rotasi lebih kecil untuk burung
        width_shift_range=0.1,    # Shift lebih kecil
        height_shift_range=0.1,
        shear_range=0.1,          # Shear lebih kecil
        zoom_range=0.15,          # Zoom lebih kecil
        horizontal_flip=True,     # Flip horizontal OK untuk burung
        vertical_flip=False,      # Tidak flip vertikal untuk burung
        fill_mode='nearest'
    )
    
    # Generate preview augmentasi
    print(f"📸 Generating augmentation preview from: {sample_image_path}")
    i = 0
    for batch in preview_datagen.flow(x_aug, batch_size=1, 
                                     save_to_dir=preview_dir, 
                                     save_prefix='bird_aug', 
                                     save_format='jpeg'):
        i += 1
        if i >= 12:  # Generate 12 preview images
            break
    
    # Tampilkan hasil preview
    preview_images = [f for f in os.listdir(preview_dir) if f.startswith('bird_aug')]
    
    if preview_images:
        plt.figure(figsize=(15, 10))
        for n, img_name in enumerate(preview_images[:12]):
            plt.subplot(3, 4, n + 1)
            plt.subplots_adjust(hspace=0.3, wspace=0.2)
            
            img = image.load_img(os.path.join(preview_dir, img_name),
                               target_size=(224, 224))
            plt.imshow(img)
            plt.title(f'Augmented {n+1}', fontsize=10)
            plt.axis('off')
        
        plt.suptitle('🐦 Bird Image Augmentation Preview', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        print(f"✅ Preview augmentasi berhasil dibuat!")
    
else:
    print("⚠️ Tidak dapat menemukan sample gambar untuk preview")

# ===== DATA GENERATORS =====
print("🔄 Creating data generators...")

# Data generator untuk training dengan augmentasi
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,
    width_shift_range=0.1,
    height_shift_range=0.1,
    shear_range=0.1,
    zoom_range=0.15,
    horizontal_flip=True,
    vertical_flip=False,  # Tidak flip vertikal untuk burung
    fill_mode='nearest',
    validation_split=0.3 # 20% untuk validasi
)

# Data generator untuk validasi (hanya rescaling)
val_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3
)

# Batch size disesuaikan untuk CNN custom
batch_size = 16
target_size = (224, 224)

# Training generator
train_generator = train_datagen.flow_from_directory(
    main_data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True
)

# Validation generator
validation_generator = val_datagen.flow_from_directory(
    main_data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False
)

print(f"\n✅ Data generators berhasil dibuat!")
print(f"📊 Training samples: {train_generator.samples}")
print(f"📊 Validation samples: {validation_generator.samples}")
print(f"📊 Number of classes: {train_generator.num_classes}")
print(f"📊 Class indices: {train_generator.class_indices}")
print(f"🔧 Batch size: {batch_size}")
print(f"📐 Target size: {target_size}")

# ===== CUSTOM CNN ARCHITECTURE =====
print("🏗️ Building Custom CNN Architecture...")

# Arsitektur CNN yang disesuaikan untuk klasifikasi burung
model = Sequential([
    # Block 1
    Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(224, 224, 3)),
    Conv2D(32, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),
    
    # Block 2
    Conv2D(64, (3, 3), activation='relu', padding='same'),
    Conv2D(64, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),
    
    # Block 3
    Conv2D(128, (3, 3), activation='relu', padding='same'),
    Conv2D(128, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),
    
    # Block 4
    Conv2D(256, (3, 3), activation='relu', padding='same'),
    Conv2D(256, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),
    
    # Classifier
    Flatten(),
    Dense(512, activation='relu'),
    Dropout(0.5),
    Dense(256, activation='relu'),
    Dropout(0.5),
    Dense(num_classes, activation='softmax')
])

# Compile model
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# Display model summary
print("\n📋 Model Architecture Summary:")
model.summary()

# Hitung total parameter
total_params = model.count_params()
print(f"\n📊 Total Parameters: {total_params:,}")
print(f"📊 Model size estimate: ~{total_params * 4 / (1024*1024):.1f} MB")