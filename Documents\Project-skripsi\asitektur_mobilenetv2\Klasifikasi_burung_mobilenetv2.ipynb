# Import libraries untuk environment lokal
import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import os
import cv2
from PIL import Image
from pathlib import Path

print("✅ Libraries imported successfully for local environment!")
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {tf.config.list_physical_devices('GPU')}")


def resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0)):
    """
    Resize gambar dengan padding untuk mempertahankan aspect ratio
    
    Args:
        image: numpy array atau PIL Image
        target_size: tuple (width, height) target size
        pad_color: tuple (R, G, B) warna padding
    
    Returns:
        numpy array gambar yang sudah diresize dengan padding
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image = Image.fromarray(image)
    
    # Get original dimensions
    original_width, original_height = image.size
    target_width, target_height = target_size
    
    # Calculate scaling factor to fit image within target size
    scale = min(target_width / original_width, target_height / original_height)
    
    # Calculate new dimensions
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    # Resize image
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)
    
    # Create new image with target size and padding color
    new_image = Image.new('RGB', target_size, pad_color)
    
    # Calculate position to paste resized image (center it)
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2
    
    # Paste resized image onto padded background
    new_image.paste(resized_image, (paste_x, paste_y))
    
    return np.array(new_image)

def resize_with_stretch(image, target_size=(224, 224)):
    """
    Resize gambar dengan stretching (tanpa mempertahankan aspect ratio)
    
    Args:
        image: numpy array atau PIL Image
        target_size: tuple (width, height) target size
    
    Returns:
        numpy array gambar yang sudah diresize
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image = Image.fromarray(image)
    
    # Resize dengan LANCZOS untuk kualitas terbaik
    resized_image = image.resize(target_size, Image.LANCZOS)
    
    return np.array(resized_image)

def preprocess_dataset(input_dir, output_dir, target_size=(224, 224), method='padding'):
    """
    Preprocess seluruh dataset dengan mengkonversi semua gambar ke target size
    
    Args:
        input_dir: path ke direktori input yang berisi subdirektori kelas
        output_dir: path ke direktori output
        target_size: tuple (width, height) target size
        method: 'padding' atau 'stretch'
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory if it doesn't exist
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    total_processed = 0
    class_counts = {}
    
    print(f"🔄 Memulai preprocessing gambar...")
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"🎯 Target size: {target_size}")
    print(f"⚙️  Method: {method}")
    print("-" * 50)
    
    # Process each class directory
    for class_dir in input_path.iterdir():
        if class_dir.is_dir():
            print(f"📂 Processing class: {class_dir.name}")
            
            # Create output class directory
            output_class_dir = output_path / class_dir.name
            output_class_dir.mkdir(exist_ok=True)
            
            # Get all image files
            image_files = [f for f in class_dir.iterdir() 
                          if f.suffix.lower() in image_extensions]
            
            class_count = 0
            
            for i, image_file in enumerate(image_files):
                try:
                    # Load image
                    image = cv2.imread(str(image_file))
                    if image is None:
                        continue
                    
                    # Convert BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # Apply preprocessing
                    if method == 'padding':
                        processed_image = resize_with_padding(image, target_size)
                    else:
                        processed_image = resize_with_stretch(image, target_size)
                    
                    # Save processed image
                    output_file = output_class_dir / f"{image_file.stem}.jpg"
                    
                    # Convert to PIL and save
                    pil_image = Image.fromarray(processed_image)
                    pil_image.save(str(output_file), 'JPEG', quality=95)
                    
                    total_processed += 1
                    class_count += 1
                    
                    if (i + 1) % 10 == 0:
                        print(f"  Processed {i + 1}/{len(image_files)} images")
                        
                except Exception as e:
                    print(f"❌ Error processing {image_file}: {e}")
            
            class_counts[class_dir.name] = class_count
            print(f"✅ {class_dir.name}: {class_count} images processed")
    
    print("-" * 50)
    print(f"🎉 Preprocessing selesai!")
    print(f"📊 Total images processed: {total_processed}")
    print("📈 Class distribution:")
    for class_name, count in class_counts.items():
        print(f"   - {class_name}: {count} images")
    
    return class_counts


input_shape = (224, 224, 3)
num_classes = 4

# ===== KONFIGURASI DATA LOKAL =====
# Script ini akan otomatis mendeteksi dan menggunakan data yang sudah ada

print("🔍 Mendeteksi dataset yang tersedia...")

# Prioritas direktori data (dari yang paling diutamakan)
data_options = [
    ('./ResizedDataset', 'Data yang sudah diproses ke 224x224 - RECOMMENDED'),
    ('./Dataset', 'Data asli dengan berbagai ukuran'),
    ('./data', 'Direktori data alternatif'),
    ('./processed_data', 'Data hasil preprocessing')
]

selected_data_dir = None
dataset_info = {}

# Cek setiap opsi direktori
for data_dir, description in data_options:
    if os.path.exists(data_dir):
        print(f"\n📁 Ditemukan: {data_dir}")
        print(f"   📝 {description}")
        
        # Cek subdirectory dan hitung gambar
        subdirs = []
        total_images = 0
        
        for item in os.listdir(data_dir):
            item_path = os.path.join(data_dir, item)
            if os.path.isdir(item_path):
                # Hitung gambar di kelas ini
                image_files = [f for f in os.listdir(item_path) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])
                if image_files:  # Hanya tambahkan jika ada gambar
                    subdirs.append({
                        'name': item,
                        'count': len(image_files)
                    })
                    total_images += len(image_files)
        
        if subdirs:  # Jika ada kelas dengan gambar
            print(f"   📊 {len(subdirs)} kelas, {total_images} gambar total")
            print(f"   📋 Kelas yang ditemukan:")
            for subdir in subdirs:
                print(f"      - {subdir['name']}: {subdir['count']} gambar")
            
            # Gunakan direktori pertama yang valid
            if not selected_data_dir:
                selected_data_dir = data_dir
                dataset_info = {
                    'classes': subdirs,
                    'total_images': total_images,
                    'num_classes': len(subdirs)
                }
                print(f"\n✅ DIPILIH: {data_dir}")
        else:
            print(f"   ⚠️  Tidak ada kelas dengan gambar yang valid")

# Hasil deteksi
if selected_data_dir:
    main_data_dir = selected_data_dir
    num_classes = dataset_info['num_classes']
    
    print(f"\n" + "="*60)
    print(f"🎯 KONFIGURASI DATA FINAL")
    print(f"="*60)
    print(f"📁 Data directory: {main_data_dir}")
    print(f"📊 Jumlah kelas: {num_classes}")
    print(f"📊 Total gambar: {dataset_info['total_images']}")
    print(f"🔧 Batch size: 32 (disesuaikan untuk environment lokal)")
    print(f"📊 Validation split: 30%")
    
    # Update num_classes variable untuk model
    print(f"\n✅ Dataset siap digunakan!")
    
else:
    print(f"\n❌ TIDAK DITEMUKAN DATASET YANG VALID!")
    print(f"\n📋 Silakan pastikan salah satu direktori berikut ada dan berisi data:")
    for data_dir, description in data_options:
        print(f"  - {data_dir} ({description})")
    print(f"\n📁 Struktur direktori yang diharapkan:")
    print(f"Dataset/ (atau ResizedDataset/)")
    print(f"  ├── Lonchura leucogastroides/")
    print(f"  ├── Lonchura maja/")
    print(f"  ├── Lonchura punctulata/")
    print(f"  └── Passer montanus/")
    print(f"\nSetiap subdirektori harus berisi gambar dari spesies burung yang sesuai.")
    
    # Fallback
    main_data_dir = './Dataset'  # Default fallback
    num_classes = 4  # Default untuk 4 kelas burung
    print(f"\n⚠️  Menggunakan fallback: {main_data_dir}")


# Data generator dengan augmentasi untuk training
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=0.3,
)

# Data generator untuk validation (hanya rescaling)
valid_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3
)


# Input data - menggunakan data yang sudah diproses ke 224x224
# Uncomment baris berikut jika menggunakan Google Colab
# drive.mount('/content/drive')
# main_data_dir = '/content/drive/MyDrive/images'
# !ls '/content/drive/MyDrive/images'

# Untuk environment lokal, main_data_dir sudah diset di cell sebelumnya
print(f"📁 Menggunakan data dari: {main_data_dir}")

# Cek apakah direktori data ada
if os.path.exists(main_data_dir):
    print(f"✅ Data directory ditemukan!")
    
    # Tampilkan info kelas dan jumlah gambar
    print("\n📊 Dataset info:")
    total_images = 0
    for class_dir in os.listdir(main_data_dir):
        class_path = os.path.join(main_data_dir, class_dir)
        if os.path.isdir(class_path):
            image_count = len([f for f in os.listdir(class_path) 
                              if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])
            total_images += image_count
            print(f"  📂 {class_dir}: {image_count} images")
    
    print(f"\n📈 Total images: {total_images}")
    
    # Set batch size (disesuaikan untuk environment lokal)
    batch_size = 32  # Reduced from 128 for local environment
    print(f"🔧 Batch size: {batch_size}")
    
    # Define train and validation generators dengan data yang sudah diproses
    print("\n🔄 Creating data generators...")
    
    train_generator = train_datagen.flow_from_directory(
        main_data_dir,
        target_size=(224, 224),  # Gambar sudah 224x224, tapi tetap specify untuk konsistensi
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=True,
        subset="training"
    )
    
    valid_generator = valid_datagen.flow_from_directory(
        main_data_dir,
        target_size=(224, 224),  # Gambar sudah 224x224, tapi tetap specify untuk konsistensi
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=False,
        subset="validation"
    )
    
    print(f"\n✅ Data generators created successfully!")
    print(f"📊 Training samples: {train_generator.samples}")
    print(f"📊 Validation samples: {valid_generator.samples}")
    print(f"📊 Number of classes: {train_generator.num_classes}")
    print(f"📊 Class indices: {train_generator.class_indices}")
    
else:
    print(f"❌ Data directory tidak ditemukan: {main_data_dir}")
    print("Silakan pastikan data sudah diproses atau sesuaikan path data.")


# Visualisasi beberapa sample gambar dari setiap kelas
def visualize_processed_samples(data_generator, num_samples=2):
    """
    Visualisasi sample gambar dari data generator
    """
    # Get class names
    class_names = list(data_generator.class_indices.keys())
    num_classes = len(class_names)
    
    # Create subplot
    fig, axes = plt.subplots(num_classes, num_samples, figsize=(num_samples*4, num_classes*3))
    if num_classes == 1:
        axes = axes.reshape(1, -1)
    if num_samples == 1:
        axes = axes.reshape(-1, 1)
    
    # Get one batch of data
    batch_images, batch_labels = next(data_generator)
    
    # Track samples per class
    class_sample_count = {i: 0 for i in range(num_classes)}
    
    # Plot samples
    for i, (image, label) in enumerate(zip(batch_images, batch_labels)):
        class_idx = np.argmax(label)
        
        if class_sample_count[class_idx] < num_samples:
            col = class_sample_count[class_idx]
            
            # Display image
            axes[class_idx, col].imshow(image)
            axes[class_idx, col].set_title(f'{class_names[class_idx]}\nShape: {image.shape}')
            axes[class_idx, col].axis('off')
            
            class_sample_count[class_idx] += 1
        
        # Break if we have enough samples for all classes
        if all(count >= num_samples for count in class_sample_count.values()):
            break
    
    # Fill empty subplots if needed
    for class_idx in range(num_classes):
        for col in range(class_sample_count[class_idx], num_samples):
            axes[class_idx, col].text(0.5, 0.5, 'No sample\navailable', 
                                    ha='center', va='center', transform=axes[class_idx, col].transAxes)
            axes[class_idx, col].axis('off')
    
    plt.tight_layout()
    plt.show()

# Jalankan visualisasi jika data generator berhasil dibuat
if 'train_generator' in locals():
    print("🖼️  Menampilkan sample gambar yang sudah diproses ke 224x224:")
    visualize_processed_samples(train_generator, num_samples=3)
    
    # Reset generator setelah visualisasi
    train_generator.reset()
else:
    print("⚠️  Data generator belum dibuat. Silakan jalankan cell sebelumnya terlebih dahulu.")


base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)

for layer in base_model.layers:
    layer.trainable = False

x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

# Combine base model and custom head
model = Model(inputs=base_model.input, outputs=predictions)

# Fine-tune the model by unfreezing some layers
for layer in base_model.layers[:-10]:
    layer.trainable = False

# Implement learning rate scheduling
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate=1e-4,
    decay_steps=1000,
    decay_rate=0.9
)
optimizer = Adam(learning_rate=lr_schedule)


model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])


# Implement early stopping callback
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=3,
    restore_best_weights=True
)

# Train the model with modified settings
history = model.fit(
    train_generator,
    epochs=30,  # Increase epochs for better training
    validation_data=valid_generator,
    callbacks=[early_stopping],
    verbose=1
)

test_loss, test_accuracy = model.evaluate(valid_generator)
print("Test Loss:", test_loss)
print("Test Accuracy:", test_accuracy)

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import confusion_matrix
# Define the path to the validation directory
validation_dir = '/content/drive/MyDrive/data_validation'

# Define the data generators
validation_datagen = ImageDataGenerator(rescale=1./255)

# Load the validation data using the ImageDataGenerator
validation_generator = validation_datagen.flow_from_directory(
    validation_dir,
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=False
)

# Get the number of classes
num_classes = len(validation_generator.class_indices)

# Define the class labels
class_labels = list(validation_generator.class_indices.keys())

# Make predictions on the validation data
validation_predictions = model.predict(validation_generator)
predicted_labels_validation = np.argmax(validation_predictions, axis=1)

# Get the true labels for the validation data
true_labels_validation = validation_generator.classes

# Calculate confusion matrix for validation data
conf_matrix_validation = confusion_matrix(true_labels_validation, predicted_labels_validation)

# Plot the confusion matrix
plt.figure(figsize=(15, 10))
sns.heatmap(conf_matrix_validation, annot=True, fmt='d', xticklabels=class_labels, yticklabels=class_labels, cmap='Reds')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title('Confusion Matrix')
plt.xticks(rotation=90)
plt.show()


model.save('birds_classification.h5')

from IPython.display import FileLink
FileLink(r'birds_classification.h5')